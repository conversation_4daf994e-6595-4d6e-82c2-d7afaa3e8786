<template>
  <div class="auth-usage-demo">
    <h2>权限系统使用示例</h2>
    
    <div class="section">
      <h3>1. 使用 useAuth composable</h3>
      <div class="demo-buttons">
        <el-button 
          v-if="hasAuth('system:user:add')" 
          type="primary"
        >
          新增用户 (system:user:add)
        </el-button>
        
        <el-button 
          v-if="hasAuth('system:user:edit')" 
          type="warning"
        >
          编辑用户 (system:user:edit)
        </el-button>
        
        <el-button 
          v-if="hasRole('admin')" 
          type="danger"
        >
          管理员功能 (admin角色)
        </el-button>
      </div>
    </div>

    <div class="section">
      <h3>2. 使用 v-auth 指令</h3>
      <div class="demo-buttons">
        <el-button v-auth="'system:user:add'" type="primary">
          新增用户 (指令)
        </el-button>
        
        <el-button v-auth="'system:user:remove'" type="danger">
          删除用户 (指令)
        </el-button>
        
        <el-button v-auth="['system:user:edit', 'system:user:view']" type="info">
          编辑或查看用户 (多权限指令)
        </el-button>
      </div>
    </div>

    <div class="section">
      <h3>3. 使用 v-roles 指令</h3>
      <div class="demo-buttons">
        <el-button v-roles="'admin'" type="primary">
          管理员按钮
        </el-button>
        
        <el-button v-roles="['admin', 'common']" type="success">
          管理员或普通用户按钮
        </el-button>
      </div>
    </div>

    <div class="section">
      <h3>4. 权限信息显示</h3>
      <div class="info-display">
        <p><strong>当前用户权限：</strong></p>
        <ul>
          <li v-for="permission in permissions" :key="permission">
            {{ permission }}
          </li>
        </ul>
        
        <p><strong>当前用户角色：</strong></p>
        <ul>
          <li v-for="role in roles" :key="role">
            {{ role }}
          </li>
        </ul>
      </div>
    </div>

    <div class="section">
      <h3>5. 工具函数使用</h3>
      <div class="demo-buttons">
        <el-button @click="testPermissions">测试权限验证</el-button>
        <el-button @click="testRoles">测试角色验证</el-button>
      </div>
      
      <div v-if="testResults.length" class="test-results">
        <h4>测试结果：</h4>
        <ul>
          <li v-for="result in testResults" :key="result">
            {{ result }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuth } from '../composables/useAuth'
import { hasPermi, hasPermiOr, hasPermiAnd, hasRole, hasRoleOr, hasRoleAnd } from '../utils/auth'

const { hasAuth, hasRole: hasRoleComposable, permissions, roles } = useAuth()
const testResults = ref<string[]>([])

const testPermissions = () => {
  testResults.value = []
  
  // 测试单个权限
  const testPerms = ['system:user:add', 'system:user:edit', 'system:user:remove', 'system:role:add']
  testPerms.forEach(perm => {
    const result = hasPermi(perm)
    testResults.value.push(`hasPermi('${perm}'): ${result}`)
  })
  
  // 测试多权限或关系
  const orResult = hasPermiOr(['system:user:add', 'system:user:edit'])
  testResults.value.push(`hasPermiOr(['system:user:add', 'system:user:edit']): ${orResult}`)
  
  // 测试多权限且关系
  const andResult = hasPermiAnd(['system:user:add', 'system:user:edit'])
  testResults.value.push(`hasPermiAnd(['system:user:add', 'system:user:edit']): ${andResult}`)
}

const testRoles = () => {
  testResults.value = []
  
  // 测试单个角色
  const testRoleList = ['admin', 'common', 'guest']
  testRoleList.forEach(role => {
    const result = hasRole(role)
    testResults.value.push(`hasRole('${role}'): ${result}`)
  })
  
  // 测试多角色或关系
  const orResult = hasRoleOr(['admin', 'common'])
  testResults.value.push(`hasRoleOr(['admin', 'common']): ${orResult}`)
  
  // 测试多角色且关系
  const andResult = hasRoleAnd(['admin', 'common'])
  testResults.value.push(`hasRoleAnd(['admin', 'common']): ${andResult}`)
}
</script>

<style scoped>
.auth-usage-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.section h3 {
  margin-top: 0;
  color: #409eff;
}

.demo-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.info-display {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.info-display ul {
  margin: 5px 0;
  padding-left: 20px;
}

.test-results {
  margin-top: 15px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 4px;
}

.test-results ul {
  margin: 10px 0;
  padding-left: 20px;
}
</style>
